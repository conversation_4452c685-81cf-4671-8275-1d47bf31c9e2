import { useState, useEffect, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useCompany } from '@/contexts/CompanyContext';

export interface DashboardFilters {
  dateRange: {
    from: Date;
    to: Date;
  };
  caseTypeId?: string;
}

export interface DashboardMetrics {
  // Lead Metrics
  totalLeads: number;
  closedLeads: number;
  closedDeals: number; // Number of leads with "עסקה סגורה" status
  closeRate: number;
  totalLeadValue: number;
  closedDealsRevenue: number; // Revenue from closed deals only
  leadsByStatus: { status: string; count: number; value: number }[];

  // Case Metrics
  totalCases: number;
  activeCases: number;
  completedCases: number;
  casesByType: { type: string; count: number; value: number }[];

  // Time & Financial Metrics
  totalHoursWorked: number;
  billableHours: number;
  averageHourlyRate: number;
  totalRevenue: number; // From closed deals (לקוח סגור)
  closedDealsCount: number;
  revenueByMonth: { month: string; revenue: number; hours: number; closedDeals: number }[];

  // Productivity Metrics
  utilizationRate: number;
  averageCaseDuration: number;
  casesByStatus: { status: string; count: number }[];
}

export const useDashboardData = (filters: DashboardFilters) => {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { currentCompany } = useCompany();

  const fetchDashboardData = async () => {
    if (!user || !currentCompany?.id) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const { from, to } = filters.dateRange;

      // Fetch leads data with company filtering
      const { data: leads, error: leadsError } = await supabase
        .from('leads')
        .select('*')
        .eq('company_id', currentCompany.id)
        .gte('created_at', from.toISOString())
        .lte('created_at', to.toISOString());

      if (leadsError) {
        throw new Error(`Failed to fetch leads: ${leadsError.message}`);
      }

      // Fetch cases data with company filtering
      let casesQuery = supabase
        .from('cases')
        .select(`
          *,
          case_types(name, hourly_rate),
          leads(full_name)
        `)
        .eq('company_id', currentCompany.id)
        .gte('created_at', from.toISOString())
        .lte('created_at', to.toISOString());

      if (filters.caseTypeId) {
        casesQuery = casesQuery.eq('case_type_id', filters.caseTypeId);
      }

      const { data: cases, error: casesError } = await casesQuery;
      if (casesError) {
        throw new Error(`Failed to fetch cases: ${casesError.message}`);
      }

      // Fetch time entries data with explicit company filtering
      const { data: timeEntries, error: timeError } = await supabase
        .from('case_time_entries')
        .select(`
          *,
          cases(title, case_types(name, hourly_rate))
        `)
        .eq('company_id', currentCompany.id)
        .gte('created_at', from.toISOString())
        .lte('created_at', to.toISOString());

      if (timeError) {
        throw new Error(`Failed to fetch time entries: ${timeError.message}`);
      }

      // Process the data
      const processedMetrics = processMetrics(leads || [], cases || [], timeEntries || []);
      setMetrics(processedMetrics);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(`Dashboard error: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Reset state when company changes to show loading
    if (currentCompany?.id) {
      setMetrics(null);
      setError(null);
    }
    fetchDashboardData();
  }, [user, currentCompany?.id, filters]);

  return { metrics, isLoading, error, refetch: fetchDashboardData };
};

function processMetrics(leads: any[], cases: any[], timeEntries: any[]): DashboardMetrics {
  // Lead Metrics
  const totalLeads = leads.length;
  const closedDealsLeads = leads.filter(lead => lead.status === 'לקוח סגור');
  const closedLeads = closedDealsLeads.length;
  const closedDealsCount = closedDealsLeads.length;
  const closeRate = totalLeads > 0 ? (closedLeads / totalLeads) * 100 : 0;
  const totalLeadValue = leads.reduce((sum, lead) => sum + (lead.value || 0), 0);
  const totalRevenue = closedDealsLeads.reduce((sum, lead) => sum + (lead.value || 0), 0);

  // Group leads by status
  const leadsByStatus = leads.reduce((acc, lead) => {
    const existing = acc.find(item => item.status === lead.status);
    if (existing) {
      existing.count++;
      existing.value += lead.value || 0;
    } else {
      acc.push({
        status: lead.status,
        count: 1,
        value: lead.value || 0
      });
    }
    return acc;
  }, [] as { status: string; count: number; value: number }[]);

  // Case Metrics
  const totalCases = cases.length;
  const activeCases = cases.filter(case_ => case_.status !== 'סגור').length;
  const completedCases = cases.filter(case_ => case_.status === 'סגור').length;

  // Group cases by type
  const casesByType = cases.reduce((acc, case_) => {
    const typeName = case_.case_types?.name || 'ללא סוג';
    const existing = acc.find(item => item.type === typeName);
    if (existing) {
      existing.count++;
      existing.value += case_.value || 0;
    } else {
      acc.push({
        type: typeName,
        count: 1,
        value: case_.value || 0
      });
    }
    return acc;
  }, [] as { type: string; count: number; value: number }[]);

  // Group cases by status
  const casesByStatus = cases.reduce((acc, case_) => {
    const existing = acc.find(item => item.status === case_.status);
    if (existing) {
      existing.count++;
    } else {
      acc.push({
        status: case_.status,
        count: 1
      });
    }
    return acc;
  }, [] as { status: string; count: number }[]);

  // Time & Financial Metrics
  const totalHoursWorked = timeEntries.reduce((sum, entry) => sum + (entry.duration || 0), 0) / 60; // Convert minutes to hours
  const billableHours = totalHoursWorked; // All logged time is considered billable
  const timeEntriesRevenue = timeEntries.reduce((sum, entry) => sum + (entry.total_cost || 0), 0);
  // Average hourly rate should be: closed deals revenue / total hours worked
  const averageHourlyRate = totalHoursWorked > 0 ? totalRevenue / totalHoursWorked : 0;

  // Total revenue is now from closed deals, not time entries
  // (totalRevenue is already calculated above)

  // Revenue by month based on closed deals (when they were closed)
  const revenueByMonth = closedDealsLeads.reduce((acc, lead) => {
    // Use updated_at as the closing date, or created_at if no updated_at
    const closingDate = lead.updated_at || lead.created_at;
    const month = new Date(closingDate).toLocaleDateString('he-IL', { year: 'numeric', month: 'short' });
    const existing = acc.find(item => item.month === month);
    const revenue = lead.value || 0;

    if (existing) {
      existing.revenue += revenue;
      existing.closedDeals += 1;
    } else {
      acc.push({ month, revenue, hours: 0, closedDeals: 1 });
    }
    return acc;
  }, [] as { month: string; revenue: number; hours: number; closedDeals: number }[]);

  // Add hours data to revenue by month from time entries
  timeEntries.forEach(entry => {
    const month = new Date(entry.created_at).toLocaleDateString('he-IL', { year: 'numeric', month: 'short' });
    const existing = revenueByMonth.find(item => item.month === month);
    const hours = (entry.duration || 0) / 60;

    if (existing) {
      existing.hours += hours;
    } else {
      revenueByMonth.push({ month, revenue: 0, hours, closedDeals: 0 });
    }
  });

  // Productivity Metrics
  const utilizationRate = totalHoursWorked > 0 ? (billableHours / totalHoursWorked) * 100 : 0;
  
  // Calculate average case duration (for completed cases)
  const completedCasesWithDuration = cases.filter(case_ => case_.status === 'סגור' && case_.created_at);
  const averageCaseDuration = completedCasesWithDuration.length > 0 
    ? completedCasesWithDuration.reduce((sum, case_) => {
        const start = new Date(case_.created_at);
        const end = new Date(); // Assuming current date as completion
        const duration = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24); // Days
        return sum + duration;
      }, 0) / completedCasesWithDuration.length
    : 0;

  return {
    totalLeads,
    closedLeads,
    closeRate,
    totalLeadValue,
    leadsByStatus,
    totalCases,
    activeCases,
    completedCases,
    casesByType,
    totalHoursWorked,
    billableHours,
    averageHourlyRate,
    totalRevenue,
    closedDealsCount,
    revenueByMonth,
    utilizationRate,
    averageCaseDuration,
    casesByStatus
  };
}
