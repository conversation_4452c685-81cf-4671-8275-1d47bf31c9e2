import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Send, Phone, MoreVertical, User } from 'lucide-react';
import { useWhatsApp, WhatsAppConversation } from '@/hooks/useWhatsApp';
import { useLeads } from '@/hooks/useLeads';
import { useCallManager } from '@/hooks/useCallManager';
import { Lead } from '@/components/leads/LeadCard';
import { format } from 'date-fns';
import { WhatsAppErrorBoundary } from '@/components/WhatsAppErrorBoundary';
import { ActiveCallBar } from '@/components/leads/ActiveCallBar';

interface WhatsAppPageProps {
  showHeader?: boolean;
  containerHeight?: string;
}

const WhatsAppPage = React.memo(function WhatsAppPage({
  showHeader = true,
  containerHeight = "h-[calc(100vh-10rem)]"
}: WhatsAppPageProps = {}) {
  // Add render tracking for development debugging
  const renderCount = useRef(0);
  renderCount.current += 1;

  // Only log in development mode with more context
  if (process.env.NODE_ENV === 'development' && renderCount.current <= 2) {
    console.log(`WhatsApp Page render #${renderCount.current}`, {
      showHeader,
      containerHeight,
      timestamp: new Date().toISOString()
    });
  }

  const [searchParams] = useSearchParams();
  const leadId = searchParams.get('lead');

  const {
    conversations,
    messages,
    isLoading,
    isSending,
    fetchMessages,
    sendMessage,
    markAsRead,
    syncWhatsApp
  } = useWhatsApp();

  const { getLeadById } = useLeads();

  const {
    activeCall,
    isLoading: isCallLoading,
    initiateCall,
    hangupCall,
    muteCall,
    unmuteCall,
    acceptCall,
    rejectCall,
    isMuted,
    device
  } = useCallManager();

  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [leadDetails, setLeadDetails] = useState<Lead | null>(null);
  const [isLeadLoading, setIsLeadLoading] = useState(false);
  const [conversationLeads, setConversationLeads] = useState<Record<string, Lead>>({});
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const selectedConversation = useMemo(() =>
    conversations.find(conv => conv.id === selectedChat),
    [conversations, selectedChat]
  );

  // Auto-scroll to bottom when messages change or conversation is selected
  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Scroll to bottom when conversation is selected
  useEffect(() => {
    if (selectedChat) {
      // Use setTimeout to ensure DOM is updated
      setTimeout(scrollToBottom, 100);
    }
  }, [selectedChat, scrollToBottom]);

  // Memoize getLeadById to prevent unnecessary re-renders
  const memoizedGetLeadById = useCallback(getLeadById, [getLeadById]);

  // Fetch lead details when leadId is provided - optimized dependencies
  useEffect(() => {
    const fetchLeadDetails = async () => {
      if (leadId && !leadDetails) {
        setIsLeadLoading(true);
        try {
          const lead = await memoizedGetLeadById(leadId);
          setLeadDetails(lead);
        } catch (error) {
          console.error('Error fetching lead details:', error);
        } finally {
          setIsLeadLoading(false);
        }
      }
    };

    fetchLeadDetails();
  }, [leadId, memoizedGetLeadById, leadDetails]);

  // Memoize fetchMessages and markAsRead to prevent unnecessary re-renders
  const memoizedFetchMessages = useCallback(fetchMessages, [fetchMessages]);
  const memoizedMarkAsRead = useCallback(markAsRead, [markAsRead]);

  // Fetch lead details for conversations - optimized to prevent infinite loops
  useEffect(() => {
    const fetchConversationLeads = async () => {
      const leadsToFetch = conversations.filter(conv => conv.lead_id && !conversationLeads[conv.lead_id]);

      if (leadsToFetch.length === 0) return; // Early return if no leads to fetch

      for (const conv of leadsToFetch) {
        if (conv.lead_id) {
          try {
            const lead = await memoizedGetLeadById(conv.lead_id);
            if (lead) {
              setConversationLeads(prev => ({ ...prev, [conv.lead_id!]: lead }));
            }
          } catch (error) {
            console.error('Error fetching lead details for conversation:', error);
          }
        }
      }
    };

    if (conversations.length > 0) {
      fetchConversationLeads();
    }
  }, [conversations, memoizedGetLeadById]); // Removed conversationLeads from deps to prevent infinite loop

  // Handle lead conversation selection - optimized
  useEffect(() => {
    if (leadId && conversations.length > 0) {
      // Find conversation for the specific lead
      const leadConversation = conversations.find(conv => conv.lead_id === leadId);
      if (leadConversation && selectedChat !== leadConversation.id) {
        setSelectedChat(leadConversation.id);
      }
    }
  }, [leadId, conversations, selectedChat]);

  // Handle messages fetch when chat is selected - optimized
  useEffect(() => {
    if (selectedChat) {
      memoizedFetchMessages(selectedChat);
      memoizedMarkAsRead(selectedChat);
    }
  }, [selectedChat, memoizedFetchMessages, memoizedMarkAsRead]);

  // Memoize sendMessage to prevent unnecessary re-renders
  const memoizedSendMessage = useCallback(sendMessage, [sendMessage]);

  const handleSendMessage = useCallback(async () => {
    if (!newMessage.trim() || isSending) return;

    try {
      if (selectedConversation) {
        // Existing conversation
        await memoizedSendMessage(
          selectedConversation.phone_number,
          newMessage.trim(),
          selectedConversation.lead_id || undefined
        );
      } else if (leadDetails) {
        // New conversation with lead
        await memoizedSendMessage(
          leadDetails.phone,
          newMessage.trim(),
          leadDetails.id
        );
      }
      setNewMessage('');
      // Scroll to bottom after sending message
      setTimeout(scrollToBottom, 100);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }, [newMessage, isSending, selectedConversation, leadDetails, memoizedSendMessage, scrollToBottom]);

  const handleSelectConversation = useCallback((conversation: WhatsAppConversation) => {
    if (selectedChat !== conversation.id) {
      setSelectedChat(conversation.id);
    }
  }, [selectedChat]);

  // Memoize format functions to prevent unnecessary re-renders
  const formatTime = useCallback((timestamp: string | null) => {
    if (!timestamp) return '';
    return format(new Date(timestamp), 'HH:mm');
  }, []);

  const formatDate = useCallback((dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  }, []);

  // Handle phone call
  const handleCall = useCallback((phoneNumber: string, leadId?: string, leadName?: string) => {
    if (leadId && leadName) {
      initiateCall(leadId, leadName, phoneNumber);
    } else {
      // Find lead by phone number if not provided
      const lead = Object.values(conversationLeads).find(l => l.phone === phoneNumber);
      if (lead) {
        initiateCall(lead.id, lead.full_name, phoneNumber);
      } else {
        console.error('Cannot initiate call: lead information not found');
      }
    }
  }, [initiateCall, conversationLeads]);

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">וואטסאפ</h1>
          <p className="text-muted-foreground mt-2">טוען שיחות...</p>
        </div>
      </div>
    );
  }

  return (
    <WhatsAppErrorBoundary>
      <div className={`${containerHeight} flex flex-col bg-background`}>
        {/* Active Call Bar */}
        {activeCall && (
          <ActiveCallBar
            activeCall={activeCall}
            onHangup={hangupCall}
            onMute={muteCall}
            onUnmute={unmuteCall}
            onAccept={acceptCall}
            onReject={rejectCall}
            isMuted={isMuted}
          />
        )}
        {/* Conditional Header */}
        {showHeader && (
          <div className="flex-shrink-0 p-6 border-b">
            <h1 className="text-3xl font-bold">וואטסאפ</h1>
            <p className="text-muted-foreground mt-2">
              נהל את השיחות וההודעות שלך בוואטסאפ
            </p>
          </div>
        )}

        {/* Main Content Area - Fixed Height */}
        <div className="flex-1 flex overflow-hidden">
          {/* Chats Container */}
          <div className="w-1/3 border-r border-border shadow-[0_0_15px_rgba(59,130,246,0.4)] border-l-2 border-l-blue-400">
            <div className="h-full flex flex-col">
              <div className="p-4 border-b border-border">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold">שיחות</h2>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => syncWhatsApp('full')}
                    className="text-xs"
                  >
                    סנכרון
                  </Button>
                </div>
              </div>
              <div className="flex-1 overflow-y-auto">
                <div className="p-2 space-y-1">
                  {conversations.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">
                      אין שיחות עדיין
                    </div>
                  ) : (
                    conversations.map((conversation) => {
                      const conversationLead = conversation.lead_id ? conversationLeads[conversation.lead_id] : null;
                      return (
                        <div
                          key={conversation.id}
                          className={`p-3 rounded-lg cursor-pointer hover:bg-accent/50 transition-colors ${
                            selectedChat === conversation.id
                              ? 'bg-primary/10 border border-primary/20'
                              : 'hover:bg-muted/50'
                          }`}
                          onClick={() => handleSelectConversation(conversation)}
                        >
                          <div className="flex items-center gap-3">
                            <Avatar className="h-10 w-10">
                              <AvatarFallback>
                                {conversation.contact_name?.split(' ').map(n => n[0]).join('') || 'U'}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h4 className="font-medium truncate">
                                  {conversation.contact_name || conversation.phone_number}
                                </h4>
                                <span className="text-xs text-muted-foreground">
                                  {conversation.last_message_timestamp ?
                                    formatTime(conversation.last_message_timestamp) : ''}
                                </span>
                              </div>
                              <p className="text-sm text-muted-foreground truncate">
                                {conversation.last_message || 'אין הודעות עדיין'}
                              </p>
                              {/* Always render the badges container to maintain consistent height */}
                              <div className="flex items-center gap-2 mt-1 min-h-[20px]">
                                {conversationLead && (
                                  <>
                                    <Badge variant="outline" className="text-xs">
                                      {conversationLead.status}
                                    </Badge>
                                    {conversationLead.value && (
                                      <Badge variant="secondary" className="text-xs text-green-600">
                                        ₪{conversationLead.value.toLocaleString()}
                                      </Badge>
                                    )}
                                  </>
                                )}
                              </div>
                            </div>
                            {conversation.unread_count > 0 && (
                              <Badge variant="default" className="ml-2">
                                {conversation.unread_count}
                              </Badge>
                            )}
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Conversations Container */}
          <div className="flex-1 shadow-[0_0_15px_rgba(59,130,246,0.4)] border-r-2 border-r-blue-400 border-l">
            <div className="h-full flex flex-col">
              {selectedConversation ? (
                <>
                  {/* Chat Header */}
                  <div className="flex items-center justify-between p-4 border-b border-border bg-muted/30">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback>
                          {selectedConversation.contact_name?.split(' ').map(n => n[0]).join('') || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">
                          {selectedConversation.contact_name || selectedConversation.phone_number}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {selectedConversation.phone_number}
                        </p>
                        {selectedConversation.lead_id && conversationLeads[selectedConversation.lead_id] && (
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {conversationLeads[selectedConversation.lead_id].status}
                            </Badge>
                            {conversationLeads[selectedConversation.lead_id].value && (
                              <Badge variant="secondary" className="text-xs">
                                ₪{conversationLeads[selectedConversation.lead_id].value.toLocaleString()}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleCall(
                          selectedConversation.phone_number,
                          selectedConversation.lead_id || undefined,
                          conversationLeads[selectedConversation.lead_id || '']?.full_name || 'Unknown'
                        )}
                        disabled={isCallLoading}
                        title="התקשר"
                      >
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Messages Area */}
                  <div className="flex-1 overflow-y-auto bg-background/50">
                    <div className="p-4 space-y-4">
                      {messages.length === 0 ? (
                        <div className="text-center text-muted-foreground py-8">
                          אין הודעות בשיחה זו
                        </div>
                      ) : (
                        messages.map((message) => (
                          <div
                            key={message.id}
                            className={`flex ${
                              message.sender_type === 'outgoing' ? 'justify-end' : 'justify-start'
                            }`}
                          >
                            <div
                              className={`max-w-[70%] rounded-lg p-3 ${
                                message.sender_type === 'outgoing'
                                  ? 'bg-primary text-primary-foreground'
                                  : 'bg-success/20 text-foreground'
                              }`}
                            >
                              <p className="text-sm">{message.content}</p>
                              <p className="text-xs opacity-70 mt-1">
                                {formatDate(message.created_at)}
                              </p>
                            </div>
                          </div>
                        ))
                      )}
                      {/* Invisible element to scroll to */}
                      <div ref={messagesEndRef} />
                    </div>
                  </div>

                  {/* Message Input */}
                  <div className="p-4 border-t bg-background/80">
                    <div className="flex gap-2">
                      <Input
                        placeholder="הקלד הודעה..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                        disabled={isSending}
                      />
                      <Button
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim() || isSending}
                        size="icon"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </>
              ) : leadDetails && !isLeadLoading ? (
                <>
                  {/* New Lead Chat Header */}
                  <div className="flex items-center justify-between p-4 border-b border-border bg-muted/30">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback>
                          <User className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{leadDetails.full_name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {leadDetails.phone}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {leadDetails.status}
                          </Badge>
                          {leadDetails.value && (
                            <Badge variant="secondary" className="text-xs">
                              ₪{leadDetails.value.toLocaleString()}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleCall(
                          leadDetails.phone,
                          leadDetails.id,
                          leadDetails.full_name
                        )}
                        disabled={isCallLoading}
                        title="התקשר"
                      >
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* New Conversation Message Area */}
                  <div className="flex-1 flex items-center justify-center bg-background/50">
                    <div className="text-center p-8">
                      <div className="bg-muted rounded-full p-4 mb-4 mx-auto w-fit">
                        <User className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium mb-2">התחל שיחה</h3>
                      <p className="text-muted-foreground mb-4 max-w-sm">
                        שלח את ההודעה הראשונה ל{leadDetails.full_name} כדי להתחיל שיחת וואטסאפ.
                      </p>
                      <div className="bg-accent/50 rounded-lg p-3 text-sm text-muted-foreground">
                        <strong>פרטי ליד:</strong><br />
                        סטטוס: {leadDetails.status}<br />
                        {leadDetails.source && `מקור: ${leadDetails.source}`}<br />
                        {leadDetails.value && `ערך: ₪${leadDetails.value.toLocaleString()}`}
                      </div>
                    </div>
                  </div>

                  {/* Message Input for New Lead */}
                  <div className="p-4 border-t bg-background/80">
                    <div className="flex gap-2">
                      <Input
                        placeholder={`שלח הודעה ראשונה ל${leadDetails.full_name}...`}
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                        disabled={isSending}
                      />
                      <Button
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim() || isSending}
                        size="icon"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center bg-background/50">
                  <div className="text-center text-muted-foreground">
                    {isLeadLoading ? (
                      <>
                        <h3 className="text-lg font-medium mb-2">טוען פרטי ליד...</h3>
                        <p>אנא המתן בזמן שאנו מביאים את פרטי הליד</p>
                      </>
                    ) : (
                      <>
                        <h3 className="text-lg font-medium mb-2">בחר שיחה</h3>
                        <p>בחר שיחה משמאל כדי להתחיל להודיע</p>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </WhatsAppErrorBoundary>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  return (
    prevProps.showHeader === nextProps.showHeader &&
    prevProps.containerHeight === nextProps.containerHeight
  );
});

export default WhatsAppPage;