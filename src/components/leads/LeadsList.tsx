import React from 'react';
import { Lead, LeadCard } from './LeadCard';
import { ScrollArea } from '@/components/ui/scroll-area';

interface LeadsListProps {
  leads: Lead[];
  onCall: (leadId: string, leadName: string, phoneNumber: string) => void;
  onEdit: (lead: Lead) => void;
  onWhatsApp: (phoneNumber: string) => void;
  isCallLoading: boolean;
  onRefresh?: () => void;
}

export const LeadsList = ({ leads, onCall, onEdit, onWhatsApp, isCallLoading, onRefresh }: LeadsListProps) => {
  if (leads.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        אין לידים להצגה
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-200px)]">
      <ScrollArea className="h-full">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-1">
          {leads.map((lead) => (
            <LeadCard
              key={lead.id}
              lead={lead}
              onCall={onCall}
              onEdit={onEdit}
              onWhatsApp={onWhatsApp}
              isCallLoading={isCallLoading}
              onRefresh={onRefresh}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};