import { Phone, Mail, MessageCircle, Edit, Eye, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AnswerStatusManager } from './AnswerStatusManager';
import { useUserManagement } from '@/hooks/useUserManagement';
import { formatPhoneForDisplay } from '@/utils/phoneUtils';

export interface Lead {
  id: string;
  full_name: string;
  phone: string;
  email?: string;
  source?: string;
  status: string;
  value?: number;
  notes?: string;
  assigned_user_id?: string;
  company_id?: string;
  created_at: string;
  updated_at: string;
  answer_status?: string;
  attempt_number?: number;
  assigned_user_email?: string;
  assigned_user_name?: string;
}

interface LeadCardProps {
  lead: Lead;
  onCall: (leadId: string, leadName: string, phoneNumber: string) => void;
  onEdit: (lead: Lead) => void;
  onWhatsApp: (phoneNumber: string) => void;
  isCallLoading: boolean;
  onRefresh?: () => void;
}

export const LeadCard = ({ lead, onCall, onEdit, onWhatsApp, isCallLoading, onRefresh }: LeadCardProps) => {
  const { users } = useUserManagement();
  const getStatusColor = (status: string) => {
    switch (status) {
      case "ליד חדש":
        return "bg-primary text-primary-foreground";
      case "צריך פולואפ":
        return "bg-warning text-warning-foreground";
      case "לקוח סגור":
        return "bg-success text-success-foreground";
      case "לא ענה":
      case "לא מעוניין":
      case "לא מתאים":
        return "bg-muted text-muted-foreground";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const formatValue = (value?: number) => {
    if (!value) return '';
    return `₪${value.toLocaleString()}`;
  };

  const formatPhone = (phone: string) => {
    return formatPhoneForDisplay(phone);
  };

  const handleWhatsApp = () => {
    if (onWhatsApp) {
      onWhatsApp(lead.phone);
    } else {
      // Navigate to WhatsApp page with lead context
      window.location.href = `/office/whatsapp?lead=${lead.id}`;
    }
  };

  const handleEmail = () => {
    if (lead.email) {
      window.open(`mailto:${lead.email}`, '_blank');
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow bg-card relative">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Status Badge */}
          <div className="flex items-center justify-between">
            <Badge className={`text-xs ${getStatusColor(lead.status)}`}>
              {lead.status}
            </Badge>
            {lead.value && (
              <span className="font-bold text-success">{formatValue(lead.value)}</span>
            )}
          </div>
          
          {/* Header */}
          <div>
            <h3 className="font-semibold text-foreground">{lead.full_name}</h3>
            <p className="text-sm text-muted-foreground">{formatPhone(lead.phone)}</p>
            {lead.assigned_user_id && (
              <div className="flex items-center gap-1 mt-1">
                <User className="w-3 h-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  {users.find(u => u.id === lead.assigned_user_id)?.full_name ||
                   users.find(u => u.id === lead.assigned_user_id)?.email ||
                   'משתמש לא נמצא'}
                </span>
              </div>
            )}
          </div>

          {/* Source and Email */}
          <div className="space-y-1">
            {lead.source && (
              <p className="text-xs text-muted-foreground">מקור: {lead.source}</p>
            )}
            {lead.email && (
              <p className="text-xs text-muted-foreground">{lead.email}</p>
            )}
          </div>

          {/* Answer Status Manager */}
          <AnswerStatusManager
            leadId={lead.id}
            initialAnswerStatus={lead.answer_status}
            initialAttemptNumber={lead.attempt_number}
            onStatusUpdate={() => onRefresh?.()}
          />

          {/* Action Buttons */}
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-1">
              <Button
                size="sm"
                variant="outline"
                className="flex items-center gap-1 text-xs px-2 py-1 h-8"
                onClick={() => onCall(lead.id, lead.full_name, lead.phone)}
                disabled={isCallLoading}
              >
                <Phone className="w-3 h-3" />
                חייג
              </Button>

              <Button
                size="sm"
                variant="outline"
                className="flex items-center gap-1 text-xs px-2 py-1 h-8"
                onClick={handleWhatsApp}
              >
                <MessageCircle className="w-3 h-3" />
                WA
              </Button>

              {lead.email && (
                <Button
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-1 text-xs px-2 py-1 h-8"
                  onClick={handleEmail}
                >
                  <Mail className="w-3 h-3" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-1">
              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0"
                onClick={() => onEdit(lead)}
              >
                <Edit className="w-3 h-3" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0"
              >
                <Eye className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};